{"name": "测试turn on show battery percentage返回正确的不支持响应", "status": "passed", "description": "验证turn on show battery percentage指令返回预期的不支持响应", "steps": [{"name": "执行命令: turn on show battery percentage", "status": "passed", "steps": [{"name": "执行命令: turn on show battery percentage", "status": "passed", "start": 1753437381221, "stop": 1753437391013}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "eedd4802-5c76-4772-b318-445ffb902125-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "121b461f-02c6-4d25-ae77-1017f8a800f9-attachment.png", "type": "image/png"}], "start": 1753437391013, "stop": 1753437391301}], "start": 1753437381221, "stop": 1753437391302}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753437391302, "stop": 1753437391306}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bb2a836c-951e-4ffe-b02d-7cf485756d1b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aa51f1e4-e50b-40a0-829b-e52bbd63b8dc-attachment.png", "type": "image/png"}], "start": 1753437391306, "stop": 1753437391613}], "attachments": [{"name": "stdout", "source": "defba3a1-0cbb-4a1e-88d6-f7df4ab486a0-attachment.txt", "type": "text/plain"}], "start": 1753437381221, "stop": 1753437391614, "uuid": "abf42e22-637d-4b98-a603-949d60c5d3ea", "historyId": "e82a80866bdbe9a7e1ac367f20c977b5", "testCaseId": "e82a80866bdbe9a7e1ac367f20c977b5", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_show_battery_percentage.TestEllaTurnShowBatteryPercentage#test_turn_on_show_battery_percentage", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_show_battery_percentage"}, {"name": "subSuite", "value": "TestEllaTurnShowBatteryPercentage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_show_battery_percentage"}]}