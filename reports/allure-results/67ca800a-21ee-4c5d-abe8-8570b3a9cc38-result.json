{"name": "测试set compatibility mode返回正确的不支持响应", "status": "passed", "description": "验证set compatibility mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: set compatibility mode", "status": "passed", "steps": [{"name": "执行命令: set compatibility mode", "status": "passed", "start": 1753436351348, "stop": 1753436360558}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "64d49d51-08a5-47f1-a7b2-82b672dbaef6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "874917ac-ff5b-4922-bcac-ae455da7d925-attachment.png", "type": "image/png"}], "start": 1753436360558, "stop": 1753436360914}], "start": 1753436351348, "stop": 1753436360915}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753436360915, "stop": 1753436360920}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "42aab28a-775f-4cab-a00a-e48cfef0ecb9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b85da1ca-34a7-4641-87ab-faa1f85b6fcb-attachment.png", "type": "image/png"}], "start": 1753436360920, "stop": 1753436361296}], "attachments": [{"name": "stdout", "source": "c8567574-721f-4fa9-bb37-3f1311095e48-attachment.txt", "type": "text/plain"}], "start": 1753436351348, "stop": 1753436361298, "uuid": "9f62628f-9175-4607-adc1-48994ed4a135", "historyId": "aecf9a6f67cd29766190cbcc133448d2", "testCaseId": "aecf9a6f67cd29766190cbcc133448d2", "fullName": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode.TestEllaSetCompatibilityMode#test_set_compatibility_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_compatibility_mode"}, {"name": "subSuite", "value": "TestEllaSetCompatibilityMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_compatibility_mode"}]}