{"name": "测试Enable Call on Hold返回正确的不支持响应", "status": "passed", "description": "验证Enable Call on Hold指令返回预期的不支持响应", "steps": [{"name": "执行命令: Enable Call on Hold", "status": "passed", "steps": [{"name": "执行命令: Enable Call on Hold", "status": "passed", "start": 1753435507275, "stop": 1753435521029}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "64fef600-5642-4b6f-ba76-3d82fb55fc1d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "be368755-0b2f-45ce-b17a-595707d3fda9-attachment.png", "type": "image/png"}], "start": 1753435521029, "stop": 1753435521395}], "start": 1753435507275, "stop": 1753435521395}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753435521395, "stop": 1753435521398}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "c70a00c5-dc88-4c60-88de-3454f257dc01-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "68d35faf-d4e2-498c-bf07-bc068cc2407e-attachment.png", "type": "image/png"}], "start": 1753435521398, "stop": 1753435521731}], "attachments": [{"name": "stdout", "source": "1bbe83c5-5b0e-44ea-b673-b6c1c01cd30b-attachment.txt", "type": "text/plain"}], "start": 1753435507275, "stop": 1753435521738, "uuid": "70d93a40-04b0-4b93-b49b-2fecd011288e", "historyId": "2bf170e8c0013ab361afb23f8f059db8", "testCaseId": "2bf170e8c0013ab361afb23f8f059db8", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold.TestEllaEnableCallHold#test_enable_call_on_hold", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_on_hold"}, {"name": "subSuite", "value": "TestEllaEnableCallHold"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_on_hold"}]}