{"name": "测试disable touch optimization返回正确的不支持响应", "status": "passed", "description": "验证disable touch optimization指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable touch optimization", "status": "passed", "steps": [{"name": "执行命令: disable touch optimization", "status": "passed", "start": 1753435272996, "stop": 1753435282555}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8fb82e33-563f-417e-9057-9e645d9a0100-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "260360ec-79db-4495-b632-8f05fc7f8b99-attachment.png", "type": "image/png"}], "start": 1753435282555, "stop": 1753435282823}], "start": 1753435272996, "stop": 1753435282824}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753435282824, "stop": 1753435282828}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b5c23d9b-1564-495d-87d8-0fada17d10a1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b6a86d2e-ef12-4dea-9225-fdf5fd4c73a1-attachment.png", "type": "image/png"}], "start": 1753435282828, "stop": 1753435283122}], "attachments": [{"name": "stdout", "source": "f81684f3-b893-46d0-b5c6-f7149bb10a12-attachment.txt", "type": "text/plain"}], "start": 1753435272995, "stop": 1753435283123, "uuid": "58b0e403-a0bd-4d1a-946e-78ce61b19667", "historyId": "0b659537bc9c9b47c2c23f702fadd56b", "testCaseId": "0b659537bc9c9b47c2c23f702fadd56b", "fullName": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization.TestEllaDisableTouchOptimization#test_disable_touch_optimization", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaDisableTouchOptimization"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization"}]}