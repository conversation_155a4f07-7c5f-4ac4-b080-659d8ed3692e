{"name": "测试close power saving mode返回正确的不支持响应", "status": "passed", "description": "验证close power saving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: close power saving mode", "status": "passed", "steps": [{"name": "执行命令: close power saving mode", "status": "passed", "start": 1753435023586, "stop": 1753435032561}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3f3be805-0423-47bc-a011-24ad0c56040b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d2af3502-f476-4adb-a4c8-befa6b7c09ea-attachment.png", "type": "image/png"}], "start": 1753435032561, "stop": 1753435032813}], "start": 1753435023586, "stop": 1753435032814}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753435032814, "stop": 1753435032816}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fa17ee44-545c-4b6c-af7c-845ad9db0828-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a73b070f-1f93-445b-ad9e-cc4f19217557-attachment.png", "type": "image/png"}], "start": 1753435032816, "stop": 1753435033092}], "attachments": [{"name": "stdout", "source": "d0bc8830-f0ca-416c-b1b7-4efc7c716ff8-attachment.txt", "type": "text/plain"}], "start": 1753435023586, "stop": 1753435033093, "uuid": "52c3bbb9-4568-4916-b6ea-01bcad7f7342", "historyId": "1da800483d0bd7f8dbe657a8d5c37f76", "testCaseId": "1da800483d0bd7f8dbe657a8d5c37f76", "fullName": "testcases.test_ella.unsupported_commands.test_close_power_saving_mode.TestEllaClosePowerSavingMode#test_close_power_saving_mode", "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaClosePowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_power_saving_mode"}]}