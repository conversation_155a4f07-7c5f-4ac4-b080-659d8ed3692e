{"uid": "c1c0b17a8a12a80f", "name": "测试set folding screen zone返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone.TestEllaSetFoldingScreenZone#test_set_folding_screen_zone", "historyId": "c04d9357fdaf44e6ee27f8a97ece6c5d", "time": {"start": 1753436551581, "stop": 1753436562246, "duration": 10665}, "description": "验证set folding screen zone指令返回预期的不支持响应", "descriptionHtml": "<p>验证set folding screen zone指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753434848016, "stop": 1753434848338, "duration": 322}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753436538101, "stop": 1753436551579, "duration": 13478}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753436551579, "stop": 1753436551579, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set folding screen zone指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set folding screen zone", "time": {"start": 1753436551581, "stop": 1753436561884, "duration": 10303}, "status": "passed", "steps": [{"name": "执行命令: set folding screen zone", "time": {"start": 1753436551581, "stop": 1753436561124, "duration": 9543}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753436561125, "stop": 1753436561881, "duration": 756}, "status": "passed", "steps": [], "attachments": [{"uid": "8f6329d470ded50c", "name": "测试总结", "source": "8f6329d470ded50c.txt", "type": "text/plain", "size": 239}, {"uid": "b0cf286cd5205ded", "name": "test_completed", "source": "b0cf286cd5205ded.png", "type": "image/png", "size": 652382}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "stepsCount": 2, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753436561884, "stop": 1753436561888, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753436561888, "stop": 1753436562246, "duration": 358}, "status": "passed", "steps": [], "attachments": [{"uid": "f9f3225a6637ee0", "name": "测试总结", "source": "f9f3225a6637ee0.txt", "type": "text/plain", "size": 239}, {"uid": "181c87670865be53", "name": "test_completed", "source": "181c87670865be53.png", "type": "image/png", "size": 652290}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "12549956543188e6", "name": "stdout", "source": "12549956543188e6.txt", "type": "text/plain", "size": 10551}], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753436562247, "stop": 1753436562247, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753436562251, "stop": 1753436563762, "duration": 1511}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753437446357, "stop": 1753437446359, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_folding_screen_zone"}, {"name": "subSuite", "value": "TestEllaSetFoldingScreenZone"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_folding_screen_zone"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c1c0b17a8a12a80f.json", "parameterValues": []}