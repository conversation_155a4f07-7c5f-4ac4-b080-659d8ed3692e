{"uid": "a53c5a2f7be8da84", "name": "测试disable magic voice changer返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "historyId": "503ff57584874e8387e6b367bfa70c8c", "time": {"start": 1753435199278, "stop": 1753435209067, "duration": 9789}, "description": "验证disable magic voice changer指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable magic voice changer指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753434848016, "stop": 1753434848338, "duration": 322}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753435185968, "stop": 1753435199277, "duration": 13309}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753435199278, "stop": 1753435199278, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证disable magic voice changer指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1753435199278, "stop": 1753435208721, "duration": 9443}, "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1753435199279, "stop": 1753435208369, "duration": 9090}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753435208369, "stop": 1753435208720, "duration": 351}, "status": "passed", "steps": [], "attachments": [{"uid": "ed90ed957983baa8", "name": "测试总结", "source": "ed90ed957983baa8.txt", "type": "text/plain", "size": 244}, {"uid": "6aff2dd4fa682f34", "name": "test_completed", "source": "6aff2dd4fa682f34.png", "type": "image/png", "size": 650481}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "stepsCount": 2, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753435208722, "stop": 1753435208724, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753435208724, "stop": 1753435209066, "duration": 342}, "status": "passed", "steps": [], "attachments": [{"uid": "b37e6722a7c46a40", "name": "测试总结", "source": "b37e6722a7c46a40.txt", "type": "text/plain", "size": 244}, {"uid": "757a448901cba6b4", "name": "test_completed", "source": "757a448901cba6b4.png", "type": "image/png", "size": 650481}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "35e87fcb034c1902", "name": "stdout", "source": "35e87fcb034c1902.txt", "type": "text/plain", "size": 10595}], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753435209071, "stop": 1753435209072, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753435209085, "stop": 1753435210489, "duration": 1404}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753437446357, "stop": 1753437446359, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_magic_voice_changer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "a53c5a2f7be8da84.json", "parameterValues": []}