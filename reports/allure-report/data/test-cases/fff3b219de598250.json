{"uid": "fff3b219de598250", "name": "测试set cover screen apps返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps.TestEllaSetCoverScreenApps#test_set_cover_screen_apps", "historyId": "154a720f41d8f5a908552e8c7cf8e781", "time": {"start": 1753436375971, "stop": 1753436386269, "duration": 10298}, "description": "验证set cover screen apps指令返回预期的不支持响应", "descriptionHtml": "<p>验证set cover screen apps指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753434848016, "stop": 1753434848338, "duration": 322}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753436362761, "stop": 1753436375970, "duration": 13209}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753436375970, "stop": 1753436375970, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证set cover screen apps指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set cover screen apps", "time": {"start": 1753436375971, "stop": 1753436385922, "duration": 9951}, "status": "passed", "steps": [{"name": "执行命令: set cover screen apps", "time": {"start": 1753436375971, "stop": 1753436385558, "duration": 9587}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753436385558, "stop": 1753436385919, "duration": 361}, "status": "passed", "steps": [], "attachments": [{"uid": "23ecc149d278ad0a", "name": "测试总结", "source": "23ecc149d278ad0a.txt", "type": "text/plain", "size": 233}, {"uid": "afa8c5b12a19a5cc", "name": "test_completed", "source": "afa8c5b12a19a5cc.png", "type": "image/png", "size": 627350}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "stepsCount": 2, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753436385922, "stop": 1753436385925, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753436385925, "stop": 1753436386268, "duration": 343}, "status": "passed", "steps": [], "attachments": [{"uid": "c112ac3e6ace77ce", "name": "测试总结", "source": "c112ac3e6ace77ce.txt", "type": "text/plain", "size": 233}, {"uid": "b74c08bca5f32441", "name": "test_completed", "source": "b74c08bca5f32441.png", "type": "image/png", "size": 627350}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "312987b0deb101a7", "name": "stdout", "source": "312987b0deb101a7.txt", "type": "text/plain", "size": 10532}], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753436386271, "stop": 1753436386271, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753436386273, "stop": 1753436387746, "duration": 1473}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753437446357, "stop": 1753437446359, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_cover_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetCoverScreenApps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_cover_screen_apps"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fff3b219de598250.json", "parameterValues": []}