{"uid": "e4189fada5003b58", "name": "测试disable zonetouch master返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master.TestEllaDisableZonetouchMaster#test_disable_zonetouch_master", "historyId": "d094a0b21c0bd532e6db707dcbab5564", "time": {"start": 1753435321644, "stop": 1753435331540, "duration": 9896}, "description": "验证disable zonetouch master指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable zonetouch master指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753434848016, "stop": 1753434848338, "duration": 322}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app", "time": {"start": 1753435309003, "stop": 1753435321642, "duration": 12639}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "take_screenshot_on_failure", "time": {"start": 1753435321643, "stop": 1753435321643, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "testStage": {"description": "验证disable zonetouch master指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable zonetouch master", "time": {"start": 1753435321644, "stop": 1753435331223, "duration": 9579}, "status": "passed", "steps": [{"name": "执行命令: disable zonetouch master", "time": {"start": 1753435321644, "stop": 1753435330939, "duration": 9295}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753435330939, "stop": 1753435331222, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "ebd5bfc0436d1f9d", "name": "测试总结", "source": "ebd5bfc0436d1f9d.txt", "type": "text/plain", "size": 232}, {"uid": "9ea34129252e2bb8", "name": "test_completed", "source": "9ea34129252e2bb8.png", "type": "image/png", "size": 624029}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [], "parameters": [], "stepsCount": 2, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753435331223, "stop": 1753435331227, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "记录测试结果", "time": {"start": 1753435331227, "stop": 1753435331539, "duration": 312}, "status": "passed", "steps": [], "attachments": [{"uid": "ec4b73763508ad9a", "name": "测试总结", "source": "ec4b73763508ad9a.txt", "type": "text/plain", "size": 232}, {"uid": "3c4dd3dac9d93ff2", "name": "test_completed", "source": "3c4dd3dac9d93ff2.png", "type": "image/png", "size": 624029}], "parameters": [], "stepsCount": 0, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 2}], "attachments": [{"uid": "3f474ba93c9f4af2", "name": "stdout", "source": "3f474ba93c9f4af2.txt", "type": "text/plain", "size": 10546}], "parameters": [], "stepsCount": 5, "hasContent": true, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 5}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753435331542, "stop": 1753435331542, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "ella_app::0", "time": {"start": 1753435331547, "stop": 1753435333006, "duration": 1459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}, {"name": "setup_test_environment::0", "time": {"start": 1753437446357, "stop": 1753437446359, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "stepsCount": 0, "hasContent": false, "attachmentStep": false, "shouldDisplayMessage": false, "attachmentsCount": 0}], "labels": [{"name": "severity", "value": "normal"}, {"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaDisableZonetouchMaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "24252-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_zonetouch_master"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e4189fada5003b58.json", "parameterValues": []}