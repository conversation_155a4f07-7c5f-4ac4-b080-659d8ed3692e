2025-07-25 17:47:07 | INFO | core.base_page:__init__:38 | 初始化页面: ella - dialogue_page
2025-07-25 17:47:07 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:66 | 🧹 开始清除手机上所有运行中的应用进程...
2025-07-25 17:47:07 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:82 | ⚡ 优先使用命令直接清理...
2025-07-25 17:47:10 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:88 | 💪 强制停止顽固应用...
2025-07-25 17:47:15 | INFO | testcases.test_ella.base_ella_test:clear_all_running_processes:98 | 🎉 应用进程清理完成，共清理 30 个应用
2025-07-25 17:47:17 | INFO | pages.apps.ella.dialogue_page:start_app:128 | 启动Ella应用
2025-07-25 17:47:17 | INFO | pages.apps.ella.dialogue_page:start_app:136 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-25 17:47:20 | INFO | pages.apps.ella.dialogue_page:_check_app_started:194 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-25 17:47:20 | INFO | pages.apps.ella.dialogue_page:start_app:141 | ✅ Ella应用启动成功（指定Activity）
2025-07-25 17:47:20 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:215 | 等待Ella页面加载完成 (超时: 15秒)
2025-07-25 17:47:20 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 15秒
2025-07-25 17:47:20 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-25 17:47:20 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-25 17:47:20 | INFO | pages.apps.ella.dialogue_page:wait_for_page_load:219 | ✅ 输入框已出现，页面加载完成
2025-07-25 17:47:20 | INFO | testcases.test_ella.base_ella_test:ella_app:863 | ✅ Ella应用启动成功
2025-07-25 17:47:20 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:890 | 初始状态None- 使用命令set scheduled power on/off and restart，状态: 
2025-07-25 17:47:20 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:494 | 确保在对话页面...
2025-07-25 17:47:20 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-25 17:47:21 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-25 17:47:21 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-25 17:47:21 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-25 17:47:21 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:505 | ✅ 已在对话页面
2025-07-25 17:47:21 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-25 17:47:21 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-25 17:47:21 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: set scheduled power on/off and restart
2025-07-25 17:47:21 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-25 17:47:21 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-25 17:47:21 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: set scheduled power on/off and restart
2025-07-25 17:47:21 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-25 17:47:21 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-25 17:47:21 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-25 17:47:22 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-25 17:47:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-25 17:47:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-25 17:47:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-25 17:47:22 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: set scheduled power on/off and restart
2025-07-25 17:47:22 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-25 17:47:22 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-25 17:47:22 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-25 17:47:22 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-25 17:47:22 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-25 17:47:23 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-25 17:47:23 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-25 17:47:23 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-25 17:47:23 | INFO | testcases.test_ella.base_ella_test:_execute_command:1159 | ✅ 成功执行命令: set scheduled power on/off and restart
2025-07-25 17:47:23 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:35 | 等待AI响应，超时时间: 8秒
2025-07-25 17:47:23 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:50 | ✅ 通过元素数量变化检测到响应
2025-07-25 17:47:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:927 | 状态检查完成，现在获取响应文本
2025-07-25 17:47:23 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:931 | 第1次尝试确保在Ella页面以获取响应
2025-07-25 17:47:23 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:494 | 确保在对话页面...
2025-07-25 17:47:23 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-25 17:47:23 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-25 17:47:23 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-25 17:47:23 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-25 17:47:24 | INFO | pages.apps.ella.dialogue_page:ensure_on_chat_page:505 | ✅ 已在对话页面
2025-07-25 17:47:24 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:935 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-25 17:47:24 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:813 | 检查是否在Ella页面...
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:484 | 检查当前进程是否是Ella...
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:491 | 当前应用: com.transsion.aivoiceassistant
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:492 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-25 17:47:24 | INFO | pages.base.system_status_checker:ensure_ella_process:501 | ✅ 当前在Ella应用进程
2025-07-25 17:47:24 | INFO | pages.apps.ella.ella_response_handler:_ensure_on_ella_page:822 | ✅ 当前在Ella页面
2025-07-25 17:47:24 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:140 | 获取AI响应文本
2025-07-25 17:47:26 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:300 | asr_txt文本不符合AI响应格式: set scheduled power on/off and restart，已达到最大重试次数
2025-07-25 17:47:27 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | robot_text节点不存在，已达到最大重试次数
2025-07-25 17:47:29 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_name节点不存在，已达到最大重试次数
2025-07-25 17:47:30 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:261 | function_control节点不存在，已达到最大重试次数
2025-07-25 17:47:30 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:154 | 尝试获取其他有效的响应文本
2025-07-25 17:47:30 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_text_views:549 | 从TextView元素获取响应
2025-07-25 17:47:31 | INFO | pages.apps.ella.ella_response_handler:_get_response_from_chat_list:565 | 查找RecyclerView中的最新消息
2025-07-25 17:47:31 | INFO | pages.apps.ella.ella_response_handler:_extract_text_from_check_area_dump:397 | 从dump正则提取文本: 5:47 Power On 7:00 AM Every day Power Off 8:00 PM MON, TUE, WED, THU, FRI Scheduled Restart 2:00 AM Wednesday Scheduled power on/off and restart
2025-07-25 17:47:31 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:166 | ✅ 获取到响应文本: 5:47 Power On 7:00 AM Every day Power Off 8:00 PM MON, TUE, WED, THU, FRI Scheduled Restart 2:00 AM Wednesday Scheduled power on/off and restart
2025-07-25 17:47:31 | WARNING | pages.apps.ella.ella_response_handler:get_response_all_text:171 | 未获取到有效的响应文本
2025-07-25 17:47:31 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:956 | 最终获取的AI响应: '['set scheduled power on/off and restart', '', '', '', '5:47 Power On 7:00 AM Every day Power Off 8:00 PM MON, TUE, WED, THU, FRI Scheduled Restart 2:00 AM Wednesday Scheduled power on/off and restart']'
2025-07-25 17:47:32 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaSetScheduledPowerOffRestart\test_completed.png
2025-07-25 17:47:32 | INFO | testcases.test_ella.base_ella_test:simple_command_test:1486 | 🎉 set scheduled power on/off and restart 测试完成
2025-07-25 17:47:32 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:1296 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['set scheduled power on/off and restart', '', '', '', '5:47 Power On 7:00 AM Every day Power Off 8:00 PM MON, TUE, WED, THU, FRI Scheduled Restart 2:00 AM Wednesday Scheduled power on/off and restart']
2025-07-25 17:47:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1343 | ⚠️ 响应未包含期望内容: 'Sorry'
2025-07-25 17:47:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1350 | ❌ 部分期望内容未找到 (0/1)
2025-07-25 17:47:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1351 | 缺失内容: ['Sorry']
2025-07-25 17:47:32 | WARNING | testcases.test_ella.base_ella_test:verify_expected_in_response:1352 | 搜索文本: 'set scheduled power on/off and restart 5:47 Power On 7:00 AM Every day Power Off 8:00 PM MON, TUE, WED, THU, FRI Scheduled Restart 2:00 AM Wednesday Scheduled power on/off and restart'
2025-07-25 17:47:32 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaSetScheduledPowerOffRestart\failure_test_set_scheduled_power_on_off_and_restart_20250725_174732.png
2025-07-25 17:47:32 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaSetScheduledPowerOffRestart\failure_test_set_scheduled_power_on_off_and_restart_20250725_174732.png
2025-07-25 17:47:32 | INFO | pages.apps.ella.dialogue_page:stop_app:235 | 停止Ella应用
2025-07-25 17:47:34 | INFO | pages.apps.ella.dialogue_page:stop_app:246 | ✅ Ella应用已成功停止
