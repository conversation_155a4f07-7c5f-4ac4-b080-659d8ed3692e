#!/usr/bin/env python3
"""
测试Ella测试生成器的优化功能
验证包含sorry或oops的响应会生成简化的测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from tools.ella_test_generator_v2 import generate_with_custom_response


def test_unsupported_command_generation():
    """测试不支持命令的生成功能"""
    print("🧪 测试不支持命令的生成功能...")
    
    # 测试用例1: 包含sorry的响应
    print("\n📋 测试1: 包含sorry的响应")
    command1 = "book a flight to paris"
    expected_response1 = ["Sorry", "I can't help with flight bookings"]
    
    file_path1 = generate_with_custom_response(command1, expected_response1)
    print(f"生成文件: {file_path1}")
    
    # 验证文件是否在unsupported_commands目录中
    assert "unsupported_commands" in file_path1, f"文件应该在unsupported_commands目录中: {file_path1}"
    print("✅ 测试1通过 - 文件生成在正确的目录中")
    
    # 测试用例2: 包含oops的响应
    print("\n📋 测试2: 包含oops的响应")
    command2 = "order pizza from dominos"
    expected_response2 = ["Oops", "I don't support food ordering yet"]
    
    file_path2 = generate_with_custom_response(command2, expected_response2)
    print(f"生成文件: {file_path2}")
    
    # 验证文件是否在unsupported_commands目录中
    assert "unsupported_commands" in file_path2, f"文件应该在unsupported_commands目录中: {file_path2}"
    print("✅ 测试2通过 - 文件生成在正确的目录中")
    
    # 测试用例3: 正常的支持命令（不包含sorry或oops）
    print("\n📋 测试3: 正常的支持命令")
    command3 = "open bluetooth"
    expected_response3 = ["Done", "Bluetooth is now on"]
    
    file_path3 = generate_with_custom_response(command3, expected_response3)
    print(f"生成文件: {file_path3}")
    
    # 验证文件不在unsupported_commands目录中
    assert "unsupported_commands" not in file_path3, f"正常命令不应该在unsupported_commands目录中: {file_path3}"
    print("✅ 测试3通过 - 正常命令生成在正确的目录中")
    
    return [file_path1, file_path2, file_path3]


def verify_generated_content(file_paths):
    """验证生成的文件内容"""
    print("\n🔍 验证生成的文件内容...")
    
    for i, file_path in enumerate(file_paths[:2], 1):  # 只检查前两个不支持的命令
        print(f"\n📄 检查文件 {i}: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 验证关键内容
        checks = [
            ("不支持的指令", "应包含'不支持的指令'标题"),
            ("verify_status=False", "不应包含状态验证"),
            ("get_response_text_smart", "应使用简化的响应获取方法"),
            ("unsupported_command", "应标记为不支持命令类型"),
            ("@allure.story(\"不支持的指令\")", "应有正确的allure标记")
        ]
        
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} - 未找到: {check_text}")
    
    # 验证正常命令的内容
    if len(file_paths) > 2:
        normal_file = file_paths[2]
        print(f"\n📄 检查正常命令文件: {normal_file}")
        
        if os.path.exists(normal_file):
            with open(normal_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 验证正常命令应该有的内容
            if "simple_command_test" in content:
                print("✅ 正常命令使用完整的测试方法")
            else:
                print("❌ 正常命令应该使用完整的测试方法")


def test_custom_output_dir_override():
    """测试自定义输出目录是否会被sorry/oops检测覆盖"""
    print("\n🧪 测试自定义输出目录覆盖功能...")
    
    command = "send email to john"
    expected_response = ["Sorry", "Email functionality is not available"]
    custom_dir = "custom_test_dir"
    
    # 即使指定了自定义目录，包含sorry的响应也应该强制使用unsupported_commands
    file_path = generate_with_custom_response(command, expected_response, output_dir_name=custom_dir)
    print(f"生成文件: {file_path}")
    
    # 验证最终还是在unsupported_commands目录中
    assert "unsupported_commands" in file_path, f"应该覆盖自定义目录，使用unsupported_commands: {file_path}"
    assert custom_dir not in file_path, f"不应该使用自定义目录: {file_path}"
    print("✅ 自定义目录覆盖测试通过")
    
    return file_path


def cleanup_test_files(file_paths):
    """清理测试生成的文件"""
    print("\n🧹 清理测试文件...")
    
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ 已删除: {file_path}")
        except Exception as e:
            print(f"❌ 删除失败 {file_path}: {e}")


if __name__ == "__main__":
    try:
        print("🚀 开始测试Ella生成器优化功能")
        print("=" * 60)
        
        # 测试不支持命令的生成
        generated_files = test_unsupported_command_generation()
        
        # 验证生成的内容
        verify_generated_content(generated_files)
        
        # 测试自定义目录覆盖
        override_file = test_custom_output_dir_override()
        generated_files.append(override_file)
        
        print("\n🎉 所有测试通过!")
        
        # 询问是否清理文件
        cleanup_choice = input("\n是否清理生成的测试文件? (y/n): ").strip().lower()
        if cleanup_choice in ['y', 'yes']:
            cleanup_test_files(generated_files)
        else:
            print("保留测试文件，您可以手动查看生成的内容")
            for file_path in generated_files:
                print(f"  📄 {file_path}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
