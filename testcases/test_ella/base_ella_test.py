"""
Ella测试基类
提供通用的测试方法和fixture，简化测试用例编写
"""
import pytest
import allure
import time
import subprocess
import json
import os
from pathlib import Path
from pages.apps.ella.dialogue_page import EllaDialoguePage
from pages.apps.ella.ella_contact_command_handler import EllaContactCommandHandler
from core.logger import log


class BaseEllaTest:
    """Ella测试基类"""

    # 类级别的配置缓存
    _status_check_config = None
    _process_cleanup_config = None
    _project_root = Path(__file__).parent.parent.parent

    @classmethod
    def _load_status_check_config(cls):
        """按需加载状态检查配置"""
        if cls._status_check_config is None:
            try:
                config_path = cls._project_root / "config" / "status_check_config.json"
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    cls._status_check_config = config_data.get("status_check_config", {})
                log.debug(f"✅ 已加载状态检查配置，共 {len(cls._status_check_config)} 项")
            except Exception as e:
                log.error(f"❌ 加载状态检查配置失败: {e}")
                cls._status_check_config = {}
        return cls._status_check_config

    @classmethod
    def _load_process_cleanup_config(cls):
        """按需加载进程清理配置"""
        if cls._process_cleanup_config is None:
            try:
                config_path = cls._project_root / "config" / "process_cleanup_config.json"
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    cls._process_cleanup_config = config_data.get("process_cleanup_config", {})
                log.debug(f"✅ 已加载进程清理配置")
            except Exception as e:
                log.error(f"❌ 加载进程清理配置失败: {e}")
                cls._process_cleanup_config = {
                    "common_user_apps": [],
                    "recent_apps_clear_positions": [],
                    "cleanup_settings": {}
                }
        return cls._process_cleanup_config

    def clear_all_running_processes(self):
        """
        清除手机上所有运行中的应用进程
        优先使用命令直接清理，Recent页面清理作为备选方案
        在测试用例执行前调用，确保测试环境干净
        """
        try:
            log.info("🧹 开始清除手机上所有运行中的应用进程...")

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})

            command_cleanup_enabled = cleanup_settings.get("command_cleanup_enabled", True)
            recent_fallback_enabled = cleanup_settings.get("recent_apps_fallback_enabled", True)
            force_stop_enabled = cleanup_settings.get("force_stop_enabled", True)
            stabilization_wait = cleanup_settings.get("stabilization_wait", 2)
            min_apps_for_fallback = cleanup_settings.get("min_apps_for_fallback", 3)

            cleared_count = 0

            # 策略1: 优先使用命令直接清理（快速、可靠）
            if command_cleanup_enabled:
                log.info("⚡ 优先使用命令直接清理...")
                command_count = self._command_clear_all_apps()
                cleared_count += command_count

            # 策略2: 强制停止特定应用（针对顽固应用如Google Maps）
            if force_stop_enabled:
                log.info("💪 强制停止顽固应用...")
                force_count = self._force_stop_stubborn_apps()
                cleared_count += force_count

            # 策略3: Recent页面清理（备选方案，当命令清理效果不佳时使用）
            if recent_fallback_enabled and cleared_count < min_apps_for_fallback:
                log.info("🎯 使用Recent页面清理作为备选方案...")
                recent_count = self._clear_via_recent_apps()
                cleared_count += recent_count

            log.info(f"🎉 应用进程清理完成，共清理 {cleared_count} 个应用")

            # 等待系统稳定
            time.sleep(stabilization_wait)

        except Exception as e:
            log.error(f"❌ 清理应用进程失败: {e}")

    def _command_clear_all_apps(self):
        """
        使用命令直接清理所有应用进程
        这是最快速和可靠的清理方式
        """
        cleared_count = 0
        try:
            log.debug("⚡ 执行命令直接清理...")

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})
            timeout = cleanup_settings.get("cleanup_timeout", 8)

            # 方法1: 获取所有运行的应用包名并逐个停止
            cleared_count += self._stop_running_apps_by_list()

            # 方法2: 使用系统级清理命令
            cleared_count += self._system_level_cleanup()

            # 方法3: 清理特定类型的应用
            cleared_count += self._clear_apps_by_category()

            log.debug(f"✅ 命令清理完成，清理了 {cleared_count} 个应用")

        except Exception as e:
            log.debug(f"命令清理异常: {e}")

        return cleared_count

    def _stop_running_apps_by_list(self):
        """
        获取运行中的应用列表并逐个停止
        """
        cleared_count = 0
        try:
            log.debug("📋 获取运行应用列表并逐个停止...")

            # 获取所有运行中的应用
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-E",
                 "mResumedActivity|mFocusedActivity"],
                capture_output=True,
                text=True,
                timeout=10,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                packages_to_stop = set()

                # 解析包名
                for line in lines:
                    if 'ComponentInfo{' in line:
                        try:
                            # 提取包名，格式如: ComponentInfo{com.example.app/com.example.app.MainActivity}
                            start = line.find('ComponentInfo{') + len('ComponentInfo{')
                            end = line.find('/', start)
                            if start > 0 and end > start:
                                package_name = line[start:end]
                                if package_name and '.' in package_name:
                                    packages_to_stop.add(package_name)
                        except:
                            continue

                # 停止找到的应用
                for package_name in packages_to_stop:
                    if self._should_stop_package(package_name):
                        try:
                            result = subprocess.run(
                                ["adb", "shell", "am", "force-stop", package_name],
                                capture_output=True,
                                text=True,
                                timeout=3
                            )
                            if result.returncode == 0:
                                cleared_count += 1
                                log.debug(f"✅ 停止运行应用: {package_name}")
                        except:
                            pass

            # 获取Recent应用列表
            result2 = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "recents"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result2.returncode == 0:
                # 解析Recent应用并停止
                recent_packages = self._parse_recent_packages(result2.stdout)
                for package_name in recent_packages:
                    if self._should_stop_package(package_name):
                        try:
                            result = subprocess.run(
                                ["adb", "shell", "am", "force-stop", package_name],
                                capture_output=True,
                                text=True,
                                timeout=3
                            )
                            if result.returncode == 0:
                                cleared_count += 1
                                log.debug(f"✅ 停止Recent应用: {package_name}")
                        except:
                            pass

        except Exception as e:
            log.debug(f"停止运行应用列表异常: {e}")

        return cleared_count

    def _system_level_cleanup(self):
        """
        执行系统级清理命令
        """
        cleared_count = 0
        try:
            log.debug("🔧 执行系统级清理命令...")

            cleanup_commands = [
                # 杀死所有后台应用
                ["adb", "shell", "am", "kill-all"],
                # 清理缓存
                ["adb", "shell", "pm", "trim-caches", "1000M"],
                # 强制垃圾回收
                ["adb", "shell", "am", "send-trim-memory", "--user", "0", "--pid", "0"],
                # 清理临时文件
                ["adb", "shell", "rm", "-rf", "/data/local/tmp/*"],
            ]

            for cmd in cleanup_commands:
                try:
                    result = subprocess.run(
                        cmd,
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    if result.returncode == 0:
                        cleared_count += 2  # 每个成功的系统命令估算清理2个应用
                        log.debug(f"✅ 执行系统命令: {' '.join(cmd[2:])}")
                except:
                    pass

        except Exception as e:
            log.debug(f"系统级清理异常: {e}")

        return cleared_count

    def _clear_apps_by_category(self):
        """
        按类别清理应用
        """
        cleared_count = 0
        try:
            log.debug("📱 按类别清理应用...")

            # 加载配置
            config = self._load_process_cleanup_config()
            common_user_apps = config.get("common_user_apps", [])

            # 按类别分组清理
            categories = {}
            for app_config in common_user_apps:
                category = app_config.get("category", "other")
                if category not in categories:
                    categories[category] = []
                categories[category].append(app_config)

            # 优先清理某些类别的应用
            priority_categories = ["social", "media", "game", "browser", "navigation"]

            for category in priority_categories:
                if category in categories:
                    for app_config in categories[category]:
                        package_name = app_config.get("package")
                        description = app_config.get("description", package_name)

                        if package_name:
                            try:
                                result = subprocess.run(
                                    ["adb", "shell", "am", "force-stop", package_name],
                                    capture_output=True,
                                    text=True,
                                    timeout=3
                                )
                                if result.returncode == 0:
                                    cleared_count += 1
                                    log.debug(f"✅ 清理{category}应用: {description}")
                            except:
                                pass

        except Exception as e:
            log.debug(f"按类别清理应用异常: {e}")

        return cleared_count

    def _should_stop_package(self, package_name):
        """
        判断是否应该停止某个包
        """
        if not package_name:
            return False

        # 不停止系统关键应用
        system_packages = [
            "com.android.systemui",
            "android",
            "com.android.phone",
            "com.android.settings",
            "com.android.launcher",
            "com.android.inputmethod",
            "com.google.android.inputmethod"
        ]

        # 不停止当前测试相关的应用
        test_packages = [
            "com.transsion.aivoiceassistant",  # Ella
        ]

        for sys_pkg in system_packages + test_packages:
            if sys_pkg in package_name.lower():
                return False

        return True

    def _parse_recent_packages(self, dumpsys_output):
        """
        解析dumpsys输出中的Recent应用包名
        """
        packages = set()
        try:
            lines = dumpsys_output.split('\n')
            for line in lines:
                if 'Task{' in line and 'A=' in line:
                    # 解析格式如: Task{123 #456 A=com.example.app U=0 StackId=1 sz=1}
                    start = line.find('A=') + 2
                    end = line.find(' ', start)
                    if start > 1 and end > start:
                        package_name = line[start:end]
                        if package_name and '.' in package_name:
                            packages.add(package_name)
        except Exception as e:
            log.debug(f"解析Recent包名异常: {e}")

        return packages

    def _gentle_clear_background_apps(self):
        """
        温和地清理后台应用（已弃用，保留用于兼容性）
        现在主要使用 _command_clear_all_apps 方法
        """
        cleared_count = 0
        try:
            log.debug("🔧 执行温和清理（兼容模式）...")

            # 简化的清理逻辑，主要作为备用
            try:
                result = subprocess.run(
                    ["adb", "shell", "am", "kill-all"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    cleared_count += 5
                    log.debug("✅ 执行了系统级清理命令")
            except:
                pass

        except Exception as e:
            log.debug(f"温和清理异常: {e}")

        return cleared_count

    def _clear_via_recent_apps(self):
        """
        模拟通过Recent页面清理所有应用
        增强版本，支持智能按钮检测和多种清理策略
        """
        cleared_count = 0
        try:
            log.debug("🎯 执行Recent页面清理操作...")

            # 加载配置
            config = self._load_process_cleanup_config()
            clear_positions = config.get("recent_apps_clear_positions", [])
            cleanup_settings = config.get("cleanup_settings", {})
            recent_cleanup_wait = cleanup_settings.get("recent_cleanup_wait", 3)
            max_retry_attempts = cleanup_settings.get("max_retry_attempts", 3)

            for attempt in range(max_retry_attempts):
                log.debug(f"第 {attempt + 1} 次尝试Recent清理...")

                # 打开Recent应用页面 (多任务页面)
                success = self._open_recent_apps_page()
                if not success:
                    log.warning(f"第 {attempt + 1} 次打开Recent页面失败")
                    continue

                # 智能检测和点击清理按钮
                button_clicked = self._smart_click_clear_button(clear_positions, attempt)

                if button_clicked:
                    log.info(f"✅ 第 {attempt + 1} 次成功点击清理按钮")
                    cleared_count += 5  # 成功点击按钮，估算清理5个应用
                    time.sleep(recent_cleanup_wait)
                    break
                else:
                    log.warning(f"第 {attempt + 1} 次未能点击清理按钮，尝试备用方法...")
                    # 使用备用清理方法
                    backup_cleared = self._backup_clear_methods()
                    cleared_count += backup_cleared

                time.sleep(1)

            # 返回主屏幕
            self._return_to_home()

            if cleared_count > 0:
                log.debug(f"✅ 完成Recent页面清理操作，清理了 {cleared_count} 个应用")
            else:
                log.warning("⚠️ Recent页面清理可能未成功")

        except Exception as e:
            log.debug(f"Recent页面清理异常: {e}")

        return cleared_count

    def _open_recent_apps_page(self):
        """
        打开Recent应用页面，支持多种方式
        """
        try:
            # 方法1: 标准的APP_SWITCH按键
            result = subprocess.run(
                ["adb", "shell", "input", "keyevent", "KEYCODE_APP_SWITCH"],
                capture_output=True,
                text=True,
                timeout=3
            )
            time.sleep(2)  # 增加等待时间

            # 验证是否成功打开Recent页面
            if self._verify_recent_page_opened():
                log.debug("✅ 使用APP_SWITCH成功打开Recent页面")
                return True

            # 方法2: 尝试长按Home键（某些设备）
            log.debug("尝试长按Home键打开Recent页面...")
            subprocess.run(
                ["adb", "shell", "input", "keyevent", "--longpress", "KEYCODE_HOME"],
                capture_output=True,
                text=True,
                timeout=3
            )
            time.sleep(2)

            if self._verify_recent_page_opened():
                log.debug("✅ 使用长按Home键成功打开Recent页面")
                return True

            # 方法3: 尝试手势导航（Android 10+）
            log.debug("尝试手势导航打开Recent页面...")
            # 从屏幕底部向上滑动并停留
            subprocess.run(
                ["adb", "shell", "input", "swipe", "540", "2000", "540", "1000", "300"],
                capture_output=True,
                text=True,
                timeout=3
            )
            time.sleep(2)

            if self._verify_recent_page_opened():
                log.debug("✅ 使用手势导航成功打开Recent页面")
                return True

            log.warning("❌ 所有方法都无法打开Recent页面")
            return False

        except Exception as e:
            log.error(f"打开Recent页面异常: {e}")
            return False

    def _verify_recent_page_opened(self):
        """
        验证Recent页面是否成功打开
        """
        try:
            # 检查当前Activity是否包含recent相关关键词
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "activities", "|", "grep", "-i", "recent"],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            if result.returncode == 0 and result.stdout.strip():
                return True

            # 备用检查：查看当前窗口信息
            result2 = subprocess.run(
                ["adb", "shell", "dumpsys", "window", "windows", "|", "grep", "-i", "recent"],
                capture_output=True,
                text=True,
                timeout=5,
                shell=True
            )

            return result2.returncode == 0 and result2.stdout.strip()

        except Exception as e:
            log.debug(f"验证Recent页面异常: {e}")
            return False

    def _smart_click_clear_button(self, clear_positions, attempt):
        """
        智能检测和点击清理按钮
        """
        try:
            # 首先尝试通过UI元素查找清理按钮
            clear_button_found = self._find_clear_button_by_ui()
            if clear_button_found:
                return True

            # 如果UI检测失败，使用配置的位置点击
            log.debug("UI检测失败，使用配置位置点击...")

            for i, position_config in enumerate(clear_positions):
                x = position_config.get("x")
                y = position_config.get("y")
                description = position_config.get("description", f"({x}, {y})")

                if x is not None and y is not None:
                    # 根据尝试次数调整点击策略
                    success = self._enhanced_tap(x, y, description, attempt)
                    if success:
                        return True

                    # 每次点击后短暂等待
                    time.sleep(0.5)

            return False

        except Exception as e:
            log.debug(f"智能点击清理按钮异常: {e}")
            return False

    def _find_clear_button_by_ui(self):
        """
        通过UI元素查找并点击清理按钮
        """
        try:
            # 获取当前屏幕的UI层次结构
            result = subprocess.run(
                ["adb", "shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode != 0:
                return False

            # 获取UI dump内容
            result2 = subprocess.run(
                ["adb", "shell", "cat", "/sdcard/ui_dump.xml"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result2.returncode != 0:
                return False

            ui_content = result2.stdout

            # 查找清理相关的按钮
            clear_keywords = ["清理", "清除", "clear", "clean", "delete", "remove", "关闭全部", "全部关闭"]

            for keyword in clear_keywords:
                if keyword.lower() in ui_content.lower():
                    # 尝试通过文本点击
                    click_result = subprocess.run(
                        ["adb", "shell", "uiautomator", "click", "--text", keyword],
                        capture_output=True,
                        text=True,
                        timeout=3
                    )

                    if click_result.returncode == 0:
                        log.debug(f"✅ 通过UI文本点击成功: {keyword}")
                        time.sleep(1)
                        return True

            return False

        except Exception as e:
            log.debug(f"UI元素查找异常: {e}")
            return False

    def _enhanced_tap(self, x, y, description, attempt):
        """
        增强的点击方法，支持多种点击策略
        """
        try:
            # 策略1: 普通点击
            result = subprocess.run(
                ["adb", "shell", "input", "tap", str(x), str(y)],
                capture_output=True,
                text=True,
                timeout=3
            )
            log.debug(f"普通点击: {description}")
            time.sleep(0.8)

            # 策略2: 如果是第2次尝试，使用长按
            if attempt >= 1:
                subprocess.run(
                    ["adb", "shell", "input", "swipe", str(x), str(y), str(x), str(y), "1000"],
                    capture_output=True,
                    text=True,
                    timeout=3
                )
                log.debug(f"长按点击: {description}")
                time.sleep(1)

            # 策略3: 如果是第3次尝试，使用双击
            if attempt >= 2:
                subprocess.run(
                    ["adb", "shell", "input", "tap", str(x), str(y)],
                    capture_output=True,
                    text=True,
                    timeout=2
                )
                time.sleep(0.2)
                subprocess.run(
                    ["adb", "shell", "input", "tap", str(x), str(y)],
                    capture_output=True,
                    text=True,
                    timeout=2
                )
                log.debug(f"双击: {description}")
                time.sleep(1)

            # 验证点击是否生效（检查是否有应用被清理）
            return self._verify_clear_action_success()

        except Exception as e:
            log.debug(f"增强点击异常: {e}")
            return False

    def _verify_clear_action_success(self):
        """
        验证清理操作是否成功
        """
        try:
            # 等待清理动画完成
            time.sleep(2)

            # 检查Recent页面是否还有应用卡片
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "activity", "recents"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                # 如果Recent列表为空或很短，说明清理成功
                lines = [line for line in result.stdout.split('\n') if line.strip()]
                if len(lines) < 5:  # 经验值：少于5行说明基本清空了
                    log.debug("✅ 清理操作验证成功")
                    return True

            return False

        except Exception as e:
            log.debug(f"验证清理操作异常: {e}")
            return False

    def _backup_clear_methods(self):
        """
        备用清理方法
        """
        cleared_count = 0
        try:
            log.debug("🔄 执行备用清理方法...")

            # 方法1: 向上滑动手势清理
            subprocess.run(
                ["adb", "shell", "input", "swipe", "540", "1200", "540", "300", "800"],
                capture_output=True,
                text=True,
                timeout=3
            )
            log.debug("执行向上滑动清理手势")
            time.sleep(1)
            cleared_count += 2

            # 方法2: 多点触控清理（某些设备支持）
            subprocess.run(
                ["adb", "shell", "input", "tap", "200", "1800"],
                capture_output=True,
                text=True,
                timeout=2
            )
            subprocess.run(
                ["adb", "shell", "input", "tap", "880", "1800"],
                capture_output=True,
                text=True,
                timeout=2
            )
            log.debug("执行多点触控清理")
            time.sleep(1)
            cleared_count += 1

            # 方法3: 侧滑清理（逐个清理应用卡片）
            for i in range(3):  # 尝试清理3个应用卡片
                y_pos = 800 + i * 200  # 不同高度的应用卡片
                subprocess.run(
                    ["adb", "shell", "input", "swipe", "200", str(y_pos), "880", str(y_pos), "300"],
                    capture_output=True,
                    text=True,
                    timeout=2
                )
                time.sleep(0.3)
            log.debug("执行侧滑清理")
            cleared_count += 3

        except Exception as e:
            log.debug(f"备用清理方法异常: {e}")

        return cleared_count

    def _return_to_home(self):
        """
        返回主屏幕
        """
        try:
            subprocess.run(
                ["adb", "shell", "input", "keyevent", "KEYCODE_HOME"],
                capture_output=True,
                text=True,
                timeout=3
            )
            time.sleep(1)
            log.debug("✅ 已返回主屏幕")
        except Exception as e:
            log.debug(f"返回主屏幕异常: {e}")

    def _force_stop_stubborn_apps(self):
        """
        强制停止顽固应用，特别针对Google Maps等难以清理的应用
        """
        cleared_count = 0
        try:
            log.debug("💪 强制停止顽固应用...")

            # 加载配置
            config = self._load_process_cleanup_config()
            cleanup_settings = config.get("cleanup_settings", {})
            timeout = cleanup_settings.get("cleanup_timeout", 8)

            # 定义顽固应用列表（优先处理这些应用）
            stubborn_apps = [
                "com.google.android.apps.maps",  # Google Maps
                "com.google.android.gms",  # Google Play Services
                "com.android.chrome",  # Chrome浏览器
                "com.facebook.katana",  # Facebook
                "com.whatsapp",  # WhatsApp
                "com.tencent.mm",  # 微信
                "com.netflix.mediaclient",  # Netflix
                "com.spotify.music"  # Spotify
            ]

            # 强制停止顽固应用
            for package_name in stubborn_apps:
                try:
                    # 方法1: 使用 am force-stop
                    result = subprocess.run(
                        ["adb", "shell", "am", "force-stop", package_name],
                        capture_output=True,
                        text=True,
                        timeout=timeout
                    )
                    if result.returncode == 0:
                        cleared_count += 1
                        log.debug(f"✅ 强制停止应用: {package_name}")

                    # 方法2: 使用 pm disable-user（临时禁用）然后重新启用
                    try:
                        subprocess.run(
                            ["adb", "shell", "pm", "disable-user", package_name],
                            capture_output=True,
                            text=True,
                            timeout=2
                        )
                        time.sleep(0.2)
                        subprocess.run(
                            ["adb", "shell", "pm", "enable", package_name],
                            capture_output=True,
                            text=True,
                            timeout=2
                        )
                        log.debug(f"🔄 重置应用状态: {package_name}")
                    except:
                        pass

                except Exception as e:
                    log.debug(f"强制停止 {package_name} 失败: {e}")

            # 额外的系统级清理命令
            try:
                # 清理系统缓存
                subprocess.run(
                    ["adb", "shell", "am", "broadcast", "-a", "android.intent.action.TRIM_MEMORY", "--ei", "level",
                     "80"],
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                log.debug("🧹 执行系统内存清理")

                # 强制垃圾回收
                subprocess.run(
                    ["adb", "shell", "am", "send-trim-memory", "--user", "0", "--pid", "0"],
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                log.debug("🗑️ 执行垃圾回收")

            except:
                pass

        except Exception as e:
            log.debug(f"强制停止顽固应用异常: {e}")

        return cleared_count

    @pytest.fixture(scope="function")
    def ella_app(self):
        """简化的Ella应用fixture"""
        ella_page = EllaDialoguePage()

        try:
            # 在启动应用前清除所有运行中的进程
            self.clear_all_running_processes()

            # 启动应用
            assert ella_page.start_app(), "Ella应用启动失败"
            assert ella_page.wait_for_page_load(timeout=15), "Ella页面加载失败"

            log.info("✅ Ella应用启动成功")
            yield ella_page

        except Exception as e:
            log.error(f"❌ Ella应用启动异常: {e}")
            pytest.fail(f"Ella应用启动异常: {e}")
        finally:
            # 清理
            try:
                ella_page.stop_app()
            except Exception as e:
                log.warning(f"⚠️ 停止应用异常: {e}")

    def execute_command_and_verify(self, ella_app, command: str, expected_status_change: bool = True):
        """
        执行命令并验证结果的通用方法

        Args:
            ella_app: Ella应用实例
            command: 要执行的命令
            expected_status_change: 是否期望状态发生变化

        Returns:
            tuple: (initial_status, final_status, response_text)
        """
        # 记录初始状态
        initial_status = self._get_initial_status(ella_app, command)
        log.info(f"初始状态{initial_status}- 使用命令{command}，状态: ")

        # 确保页面就绪
        self._ensure_page_ready(ella_app)

        # 执行命令
        self._execute_command(ella_app, command)

        # 等待响应（但不立即获取响应文本）
        response_received = ella_app.wait_for_response(timeout=8)
        if not response_received:
            log.warning("⚠️ 响应超时")
            time.sleep(3)

        # 验证最终状态（此时可能在目标应用页面，使用smart方法会自动返回Ella）
        final_status = self._get_final_status(ella_app, command)

        # 在确认状态后，确保回到Ella页面获取响应文本
        response_text = self._wait_and_get_response_after_status_check(ella_app)

        # 验证状态变化
        if expected_status_change:
            self._verify_status_change(initial_status, final_status, command)

        return initial_status, final_status, response_text

    def _wait_and_get_response_after_status_check(self, ella_app, max_return_attempts: int = 3):
        """
        在状态检查后获取响应文本

        Args:
            ella_app: Ella应用实例
            max_return_attempts: 最大返回Ella应用尝试次数

        Returns:
            list: 响应文本列表
        """
        log.info("状态检查完成，现在获取响应文本")

        # 多次尝试确保回到Ella页面
        for attempt in range(max_return_attempts):
            log.info(f"第{attempt + 1}次尝试确保在Ella页面以获取响应")

            # 检查是否在Ella对话页面
            if ella_app.ensure_on_chat_page():
                log.info("✅ 已确认在Ella对话页面，可以获取响应")
                break

            # 如果不在，尝试返回
            log.warning(f"不在Ella对话页面，第{attempt + 1}次尝试返回")
            if ella_app.return_to_ella_app():
                time.sleep(2)
                # 再次检查是否成功返回
                if ella_app.ensure_on_chat_page():
                    log.info(f"✅ 第{attempt + 1}次尝试成功返回Ella页面")
                    break
                else:
                    log.warning(f"第{attempt + 1}次返回后仍不在Ella页面")
            else:
                log.error(f"第{attempt + 1}次返回Ella应用失败")
        else:
            # 所有尝试都失败
            log.error(f"经过{max_return_attempts}次尝试仍无法返回Ella页面，强制获取响应")

        # 获取响应文本
        response_text = self._safe_get_response_text(ella_app)
        log.info(f"最终获取的AI响应: '{response_text}'")
        return response_text

    @property
    def STATUS_CHECK_CONFIG(self):
        """状态检查配置映射（按需加载）"""
        return self._load_status_check_config()

    def _detect_command_type(self, command: str) -> str:
        """
        检测命令类型

        Args:
            command: 输入命令

        Returns:
            str: 命令类型，如果未识别返回None
        """
        command_lower = command.lower()

        for cmd_type, config in self.STATUS_CHECK_CONFIG.items():
            if any(keyword in command_lower for keyword in config['keywords']):
                log.debug(f"检测到命令类型: {cmd_type} ({config['description']})")
                return cmd_type

        log.debug(f"未识别的命令类型: {command}")
        return None

    def _get_status_by_type(self, ella_app, command_type: str, is_final: bool = False,
                            wait_time: float = 3.0) -> any:
        """
        根据命令类型获取状态

        Args:
            ella_app: Ella应用实例
            command_type: 命令类型
            is_final: 是否为最终状态检查
            wait_time: 最终状态检查前的等待时间

        Returns:
            any: 状态值，如果无法获取返回None
        """
        if not command_type or command_type not in self.STATUS_CHECK_CONFIG:
            return None

        config = self.STATUS_CHECK_CONFIG[command_type]

        # 如果是最终状态检查，先等待状态变化
        if is_final and wait_time > 0:
            log.debug(f"等待状态变化: {wait_time}秒")
            time.sleep(wait_time)

        # 选择合适的检查方法
        method_name = config['final_method'] if is_final else config['initial_method']

        try:
            # 动态调用方法
            if hasattr(ella_app, method_name):
                method = getattr(ella_app, method_name)
                status = method()
                log.debug(f"获取{config['description']}{'(最终)' if is_final else '(初始)'}: {status}")
                return status
            else:
                log.warning(f"方法不存在: {method_name}")
                return None

        except Exception as e:
            log.error(f"获取{config['description']}失败: {e}")
            return None

    def _get_initial_status(self, ella_app, command: str):
        """
        获取初始状态

        Args:
            ella_app: Ella应用实例
            command: 输入命令

        Returns:
            any: 初始状态值
        """
        command_type = self._detect_command_type(command)
        return self._get_status_by_type(ella_app, command_type, is_final=False)

    def _get_final_status(self, ella_app, command: str, wait_time: float = 3.0):
        """
        获取最终状态

        Args:
            ella_app: Ella应用实例
            command: 输入命令
            wait_time: 等待状态变化的时间（秒）

        Returns:
            any: 最终状态值
        """
        command_type = self._detect_command_type(command)
        return self._get_status_by_type(ella_app, command_type, is_final=True, wait_time=wait_time)

    def get_status_by_command(self, ella_app, command: str, is_final: bool = False,
                              wait_time: float = 3.0) -> dict:
        """
        通用方法：根据命令获取状态信息

        Args:
            ella_app: Ella应用实例
            command: 输入命令
            is_final: 是否为最终状态检查
            wait_time: 最终状态检查前的等待时间

        Returns:
            dict: 包含状态信息的字典
        """
        command_type = self._detect_command_type(command)

        if not command_type:
            return {
                'command_type': None,
                'status': None,
                'description': '未识别的命令类型',
                'success': False
            }

        config = self.STATUS_CHECK_CONFIG[command_type]
        status = self._get_status_by_type(ella_app, command_type, is_final, wait_time)

        return {
            'command_type': command_type,
            'status': status,
            'description': config['description'],
            'success': status is not None,
            'is_final': is_final
        }

    def add_custom_status_check(self, command_type: str, keywords: list,
                                initial_method: str, final_method: str = None,
                                description: str = None):
        """
        添加自定义状态检查配置

        Args:
            command_type: 命令类型标识
            keywords: 关键词列表
            initial_method: 初始状态检查方法名
            final_method: 最终状态检查方法名（可选，默认与initial_method相同）
            description: 描述信息（可选）
        """
        if final_method is None:
            final_method = initial_method

        if description is None:
            description = f"{command_type}状态"

        # 确保配置已加载
        config = self._load_status_check_config()

        # 动态添加配置
        config[command_type] = {
            'keywords': keywords,
            'initial_method': initial_method,
            'final_method': final_method,
            'description': description
        }

        log.info(f"添加自定义状态检查配置: {command_type} -> {description}")

    def get_supported_command_types(self) -> list:
        """
        获取支持的命令类型列表

        Returns:
            list: 支持的命令类型列表
        """
        return list(self.STATUS_CHECK_CONFIG.keys())

    def get_command_keywords(self, command_type: str = None) -> dict:
        """
        获取命令关键词映射

        Args:
            command_type: 特定命令类型（可选）

        Returns:
            dict: 关键词映射字典
        """
        if command_type:
            if command_type in self.STATUS_CHECK_CONFIG:
                return {command_type: self.STATUS_CHECK_CONFIG[command_type]['keywords']}
            else:
                return {}

        return {cmd_type: config['keywords']
                for cmd_type, config in self.STATUS_CHECK_CONFIG.items()}

    def _ensure_page_ready(self, ella_app):
        """确保页面就绪"""
        assert ella_app.ensure_on_chat_page(), "无法确保在对话页面"
        assert ella_app.ensure_input_box_ready(), "输入框未就绪"

    def _execute_command(self, ella_app, command: str):
        """执行命令"""
        success = ella_app.execute_text_command(command)
        assert success, f"执行命令失败: {command}"
        log.info(f"✅ 成功执行命令: {command}")

    def _wait_and_get_response(self, ella_app, timeout: int = 8, max_return_attempts: int = 3):
        """
        等待并获取响应

        Args:
            ella_app: Ella应用实例
            timeout: 等待响应超时时间
            max_return_attempts: 最大返回Ella应用尝试次数

        Returns:
            list: 响应文本列表
        """
        # 等待AI响应
        response_received = ella_app.wait_for_response(timeout=timeout)

        if not response_received:
            log.warning("⚠️ 响应超时，尝试直接获取")
            time.sleep(3)

        # 确保返回到Ella应用以获取响应文本
        log.info("确保返回到Ella应用以获取响应文本")

        # 多次尝试确保回到Ella页面
        for attempt in range(max_return_attempts):
            log.info(f"第{attempt + 1}次尝试确保在Ella页面")

            # 检查是否在Ella对话页面
            if ella_app.ensure_on_chat_page():
                log.info("✅ 已确认在Ella对话页面")
                break

            # 如果不在，尝试返回
            log.warning(f"不在Ella对话页面，第{attempt + 1}次尝试返回")
            if ella_app.return_to_ella_app():
                time.sleep(2)
                # 再次检查是否成功返回
                if ella_app.ensure_on_chat_page():
                    log.info(f"✅ 第{attempt + 1}次尝试成功返回Ella页面")
                    break
                else:
                    log.warning(f"第{attempt + 1}次返回后仍不在Ella页面")
            else:
                log.error(f"第{attempt + 1}次返回Ella应用失败")
        else:
            # 所有尝试都失败
            log.error(f"经过{max_return_attempts}次尝试仍无法返回Ella页面，强制获取响应")

        # 获取响应文本
        response_text = self._safe_get_response_text(ella_app)
        log.info(f"AI响应: '{response_text}'")
        return response_text

    def _safe_get_response_text(self, ella_app) -> list:
        """
        安全获取响应文本

        Args:
            ella_app: Ella应用实例

        Returns:
            list: 响应文本列表
        """
        try:
            # 优先使用智能方法获取所有文本
            response_text = ella_app.get_response_all_text()

            # 如果获取失败或为空，尝试备用方法
            if not response_text or (isinstance(response_text, list) and not any(response_text)):
                log.warning("get_response_all_text()返回空，尝试备用方法")
                backup_text = ella_app.get_response_text()
                if backup_text:
                    response_text = [backup_text]
                else:
                    log.warning("所有响应获取方法都返回空")
                    response_text = []

            # 确保返回列表格式
            if isinstance(response_text, str):
                response_text = [response_text]
            elif not isinstance(response_text, list):
                response_text = []

            return response_text

        except Exception as e:
            log.error(f"获取响应文本异常: {e}")
            return []

    def _verify_status_change(self, initial_status, final_status, command: str):
        """验证状态变化"""
        if "bluetooth" in command.lower():
            if "open" in command.lower():
                assert final_status, f"蓝牙未开启: 初始={initial_status}, 最终={final_status}"
            elif "close" in command.lower():
                assert not final_status, f"蓝牙未关闭: 初始={initial_status}, 最终={final_status}"
        elif ("contact" in command.lower() or "contacts" in command.lower()) and "open" in command.lower():
            assert final_status, f"联系人应用未打开: 初始={initial_status}, 最终={final_status}"

        log.info(f"✅ 状态验证通过: {initial_status} -> {final_status}")

    def create_test_summary(self, command: str, initial_status, final_status, response_text: str):
        """创建测试总结"""
        status_change = "是" if initial_status != final_status else "否"

        summary = f"""
测试命令: {command}
响应内容: {response_text}
初始状态: {initial_status}
最终状态: {final_status}
状态变化: {status_change}
测试结果: 成功
"""
        return summary.strip()

    def attach_test_summary(self, summary: str):
        """附加测试总结到Allure报告"""
        allure.attach(summary, name="测试总结", attachment_type=allure.attachment_type.TEXT)

    def take_screenshot(self, ella_app, name: str):
        """截图并附加到Allure报告"""
        screenshot_path = ella_app.screenshot(f"{name}.png")
        allure.attach.file(screenshot_path, name=name, attachment_type=allure.attachment_type.PNG)
        return screenshot_path

    def verify_expected_in_response(self, expected_text, response_text):
        """
        验证期望内容是否在响应中

        Args:
            expected_text: 期望的文本内容，可以是字符串或字符串列表
            response_text: 响应文本，可以是字符串或字符串列表

        Returns:
            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含
        """
        log.info(f"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}")

        # 处理 expected_text 参数
        if isinstance(expected_text, str):
            expected_list = [expected_text]
        elif isinstance(expected_text, list):
            expected_list = expected_text
        else:
            log.error(f"❌ expected_text类型错误: {type(expected_text)}")
            return False

        # 处理 response_text 参数，统一转换为字符串进行搜索
        if isinstance(response_text, str):
            # 如果是字符串，直接使用
            search_text = response_text
            log.debug(f"响应文本(字符串): {search_text}")
        elif isinstance(response_text, list):
            # 如果是列表，过滤空值并合并为一个字符串
            filtered_texts = [text for text in response_text if text and text.strip()]
            search_text = " ".join(filtered_texts)
            log.debug(f"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}")
        else:
            log.error(f"❌ response_text类型错误: {type(response_text)}")
            return False

        # 如果合并后的文本为空，记录警告
        if not search_text or not search_text.strip():
            log.warning("⚠️ 响应文本为空或只包含空白字符")
            search_text = ""

        # 记录所有验证结果
        all_found = True
        found_items = []
        missing_items = []

        # 遍历所有期望内容
        for expected_item in expected_list:
            if not expected_item or not expected_item.strip():
                log.warning(f"⚠️ 跳过空的期望内容: '{expected_item}'")
                continue

            # 在合并的文本中搜索
            if expected_item.lower() in search_text.lower():
                found_items.append(expected_item)
                log.info(f"✅ 响应包含期望内容: '{expected_item}'")
            else:
                missing_items.append(expected_item)
                log.warning(f"⚠️ 响应未包含期望内容: '{expected_item}'")
                all_found = False

        # 输出总结
        if all_found:
            log.info(f"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})")
        else:
            log.warning(f"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})")
            log.warning(f"缺失内容: {missing_items}")
            log.warning(f"搜索文本: '{search_text}'")

        assert all_found, f"响应未包含期望内容: {missing_items}"
        return all_found

    def verify_expected_in_response_advanced(self, expected_text, response_text,
                                             search_mode: str = "combined",
                                             match_any: bool = False):
        """
        高级版本的响应验证方法，支持多种搜索模式

        Args:
            expected_text: 期望的文本内容，可以是字符串或字符串列表
            response_text: 响应文本，可以是字符串或字符串列表
            search_mode: 搜索模式
                - "combined": 将列表合并为一个字符串搜索（默认）
                - "individual": 在列表的每个元素中分别搜索
                - "any_item": 在列表中任意一个元素包含即可
            match_any: 是否只要匹配任意一个期望内容即可（默认False，需要全部匹配）

        Returns:
            bool: 验证是否通过
        """
        log.info(
            f"verify_expected_in_response_advanced 响应类型: {type(response_text)}, 搜索模式: {search_mode}, 匹配模式: {'任意匹配' if match_any else '全部匹配'}")

        # 处理 expected_text 参数
        if isinstance(expected_text, str):
            expected_list = [expected_text]
        elif isinstance(expected_text, list):
            expected_list = expected_text
        else:
            log.error(f"❌ expected_text类型错误: {type(expected_text)}")
            return False

        # 处理 response_text 参数
        if isinstance(response_text, str):
            response_list = [response_text]
        elif isinstance(response_text, list):
            # 过滤空值
            response_list = [text for text in response_text if text and text.strip()]
        else:
            log.error(f"❌ response_text类型错误: {type(response_text)}")
            return False

        if not response_list:
            log.warning("⚠️ 响应文本列表为空")
            return False

        # 记录验证结果
        found_items = []
        missing_items = []

        # 根据搜索模式进行验证
        for expected_item in expected_list:
            if not expected_item or not expected_item.strip():
                log.warning(f"⚠️ 跳过空的期望内容: '{expected_item}'")
                continue

            item_found = False

            if search_mode == "combined":
                # 合并模式：将所有响应文本合并后搜索
                combined_text = " ".join(response_list)
                if expected_item.lower() in combined_text.lower():
                    item_found = True
                    log.info(f"✅ [合并模式] 找到期望内容: '{expected_item}'")

            elif search_mode == "individual":
                # 独立模式：在每个响应文本中分别搜索
                for i, response_item in enumerate(response_list):
                    if expected_item.lower() in response_item.lower():
                        item_found = True
                        log.info(f"✅ [独立模式] 在响应项{i + 1}中找到期望内容: '{expected_item}' -> '{response_item}'")
                        break

            elif search_mode == "any_item":
                # 任意项模式：只要在任意一个响应文本中找到即可
                for i, response_item in enumerate(response_list):
                    if expected_item.lower() in response_item.lower():
                        item_found = True
                        log.info(
                            f"✅ [任意项模式] 在响应项{i + 1}中找到期望内容: '{expected_item}' -> '{response_item}'")
                        break

            else:
                log.error(f"❌ 不支持的搜索模式: {search_mode}")
                return False

            # 记录结果
            if item_found:
                found_items.append(expected_item)
                if match_any:
                    # 如果是任意匹配模式，找到一个就可以返回成功
                    log.info(f"🎉 [任意匹配模式] 找到期望内容，验证通过: '{expected_item}'")
                    return True
            else:
                missing_items.append(expected_item)
                log.warning(f"⚠️ 未找到期望内容: '{expected_item}'")

        # 输出总结
        all_found = len(missing_items) == 0
        if all_found:
            log.info(f"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})")
        else:
            log.warning(f"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})")
            log.warning(f"缺失内容: {missing_items}")
            log.warning(f"响应内容: {response_list}")

        return all_found


class SimpleEllaTest(BaseEllaTest):
    """极简版Ella测试基类"""

    def simple_command_test(self, ella_app, command: str, verify_status: bool = True):
        """
        极简命令测试方法
        
        Args:
            ella_app: Ella应用实例
            command: 要执行的命令
            verify_status: 是否验证状态变化
        """
        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text = self.execute_command_and_verify(
                ella_app, command, verify_status
            )

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        log.info(f"🎉 {command} 测试完成")
        return initial_status, final_status, response_text

    def execute_contact_command_test(self, ella_app, command: str):
        """
        执行联系人命令测试的专用方法

        Args:
            ella_app: Ella应用实例
            command: 要执行的命令

        Returns:
            tuple: (initial_status, final_status, response_text)
        """
        # 创建联系人命令处理器
        contact_handler = EllaContactCommandHandler(ella_app)

        with allure.step(f"执行联系人命令: {command}"):
            initial_status, final_status, response_text = contact_handler.execute_contact_command_with_retry(command)

        with allure.step("验证联系人命令结果"):
            verified_status = contact_handler.verify_contact_command_result(initial_status, final_status, response_text)
            if verified_status and not final_status:
                # 如果验证通过但原始检测失败，更新最终状态
                final_status = True

        return initial_status, final_status, response_text


if __name__ == '__main__':
    base_test = BaseEllaTest()
    base_test.clear_all_running_processes()
